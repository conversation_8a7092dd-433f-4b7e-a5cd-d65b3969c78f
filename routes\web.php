<?php

use App\Models\User;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CategoryController;

Route::get('/', function () {
    return view('welcome');
});

// PDF export routes (must be before resource routes)
Route::get('users/export/pdf', [UserController::class, 'exportPdf'])->name('users.export.pdf');
Route::get('users/{user}/export/pdf', [UserController::class, 'exportUserPdf'])->name('users.export.user.pdf');
Route::resource('users', UserController::class);

// Categories
Route::resource('categories', CategoryController::class);
// Products
Route::resource('products', ProductController::class);

//orders
Route::get('orders/{order}/export/pdf', [OrderController::class, 'exportOrderPdf'])->name('orders.export.pdf');
Route::resource('orders', OrderController::class);
Route::post('orders/{order}/status', [OrderController::class, 'updateStatus'])
    ->name('orders.update-status');
