<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Define realistic category names
        $categoryNames = [
            'Electronics',
            'Clothing',
            'Books',
            'Sports',
            'Home & Garden',
            'Automotive',
            'Health & Beauty',
            'Toys & Games',
            'Food & Beverages',
            'Music & Movies',
            'Office Supplies',
            'Pet Supplies',
            'Jewelry',
            'Baby & Kids',
            'Travel & Luggage',
            'Art & Crafts',
            'Industrial',
            'Furniture',
            'Appliances',
            'Tools & Hardware'
        ];

        $categoryName = $this->faker->unique()->randomElement($categoryNames);

        // Generate 3-letter code from category name
        // Remove spaces and special characters, take first 3 letters
        $cleanName = preg_replace('/[^a-zA-Z]/', '', $categoryName);
        $code = strtoupper(substr($cleanName, 0, 3));

        // Ensure we have at least 3 characters, pad if necessary
        if (strlen($code) < 3) {
            $code = str_pad($code, 3, 'X', STR_PAD_RIGHT);
        }

        return [
            'code' => $code,
            'name' => $categoryName,
            'description' => $this->faker->sentence(rand(3, 5)),
        ];
    }
}
