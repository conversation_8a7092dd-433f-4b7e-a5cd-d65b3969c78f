<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Database\Seeders\ArabicUsersSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create regular users
        //User::factory(10)->create();

        //Create Arabic users
        //$this->call(ArabicUsersSeeder::class);

        // Créer d'abord les catégories
        //Category::factory(20)->create();

        // Create regular products
        Product::factory(150)->create();

        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
