<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Il est plus efficace de créer les catégories d'abord dans le seeder,
        // puis de créer les produits. Cette factory choisira une catégorie au hasard.
        $category = Category::inRandomOrder()->first();

        // Solution de repli au cas où aucune catégorie n'est amorcée en premier.
        if (!$category) {
            $category = Category::factory()->create();
        }

        return [
            'category_id' => $category->id,
            'code' => $category->code . '-' . $this->faker->unique()->numerify('####'),
            'name' => ucwords($this->faker->words(rand(1, 2), true)),
            'price' => $this->faker->randomFloat(2, 5, 2000),
            'quantity' => $this->faker->numberBetween(0, 200),
            'description' => $this->faker->paragraph(rand(2, 3)),
        ];
    }
}
