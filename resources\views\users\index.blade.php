@extends('layouts.app')
@section('title', 'Users List')
@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>Users List</h2>
                    <div>
                        <a href="{{ route('users.export.pdf') }}" class="btn btn-success btn-sm me-2">
                            <i class="bi bi-file-pdf"></i> تصدير PDF
                        </a>
                        <a href="{{ route('users.create') }}" class="btn btn-dark btn-sm">Add user</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($users as $user)
                            <tr>
                                <th scope="row">{{ $user->id }}</th>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->phone }}</td>
                                <td>{{ $user->address }}</td>

                                <td class="d-flex justify-content-center align-items-center">
                                    <a href="{{ route('users.show', $user) }}" class="btn btn-primary btn-sm"><i
                                            class="bi bi-eye"></i></a>
                                    <a href="{{ route('users.edit', $user) }}" class="btn btn-warning btn-sm"><i
                                            class="bi bi-pencil"></i></a>
                                    <form action="{{ route('users.destroy', $user) }}" method="POST"
                                        style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm delete-btn"
                                            data-toggle="tooltip" title='Delete' data-name="{{ $user->name }}"><i
                                                class="bi bi-trash"></i></button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                <div class="mt-3">
                    {{ $users->links('pagination::bootstrap-5') }}
                </div>
            </div>
        </div>
    </div>
@endsection
