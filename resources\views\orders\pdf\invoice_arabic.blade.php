<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ $order->order_number }}</title>
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            background-color: #ffffff;
            direction: rtl;
            font-size: 10pt;
            margin: 0;
            padding: 20px;
        }

        .invoice-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border: 1px solid #dee2e6;
            padding: 20px;
        }

        .invoice-header {
            background-color: #4a5568;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .company-logo {
            max-width: 120px;
            height: auto;
            border-radius: 8px;
        }

        .invoice-title {
            font-size: 24pt;
            font-weight: bold;
            margin: 10px 0;
        }

        .company-details {
            font-size: 9pt;
            margin: 0;
        }

        .company-details p {
            margin: 2px 0;
        }

        .invoice-body {
            padding: 20px 0;
        }

        .info-table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }

        .info-table td {
            width: 50%;
            vertical-align: top;
            padding: 10px;
        }

        .info-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            height: 100%;
        }

        .section-title {
            color: #495057;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-size: 12pt;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }

        .items-table thead {
            background-color: #343a40;
            color: white;
        }

        .items-table th {
            font-weight: bold;
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .items-table td {
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .totals-table {
            width: 50%;
            float: left;
            margin-top: 20px;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }

        .final-total {
            font-weight: bold;
            border-top: 2px solid #495057;
            font-size: 12pt;
        }

        .invoice-footer {
            background: #495057;
            color: white;
            padding: 15px;
            text-align: center;
            margin-top: 30px;
            clear: both;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.8rem;
            color: #fff;
        }

        .status-completed {
            background-color: #28a745;
        }

        .status-processing {
            background-color: #ffc107;
            color: #000;
        }

        .status-pending {
            background-color: #dc3545;
        }

        .status-cancelled {
            background-color: #6c757d;
        }

        .info-label {
            font-weight: bold;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            color: #495057;
            margin-bottom: 10px;
        }

        @media print {
            body {
                background: white !important;
                padding: 0;
            }

            .invoice-container {
                border: none;
                margin: 0;
                max-width: none;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 25%; text-align: right;">
                        @php
                            $logoPath = public_path('assets/images/logo.png');
                        @endphp
                        @if (file_exists($logoPath))
                            <img src="{{ $logoPath }}" alt="Logo" class="company-logo">
                        @else
                            <div style="font-size: 20px; font-weight: bold; color: #ccc;">LOGO</div>
                        @endif
                    </td>
                    <td style="width: 50%; text-align: center;">
                        <h1 class="invoice-title">فـاتـورة</h1>
                        <h4>الإدارة العامة للأشغال</h4>
                    </td>
                    <td style="width: 25%; text-align: left; vertical-align: top;">
                        <div class="company-details">
                            <p>رقم الفاتورة: #{{ $order->order_number }}</p>
                            <p>القاعدة العسكرية بالعوينة</p>
                            <p>الهاتف: 28341663</p>
                            <p><EMAIL></p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <!-- Body -->
        <div class="invoice-body">
            <table class="info-table">
                <tr>
                    <td>
                        <div class="info-card">
                            <h5 class="section-title">معلومات الطلب</h5>
                            <div class="info-label">رقم الطلب</div>
                            <div class="info-value">#{{ $order->order_number }}</div>

                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ $order->created_at->format('Y/m/d - H:i') }}</div>

                            <div class="info-label">حالة الطلب</div>
                            <div class="info-value">
                                @if ($order->status === 'completed')
                                    <span class="status-badge status-completed">مكتمل</span>
                                @elseif($order->status === 'processing')
                                    <span class="status-badge status-processing">قيد المعالجة</span>
                                @elseif($order->status === 'pending')
                                    <span class="status-badge status-pending">في الانتظار</span>
                                @elseif($order->status === 'cancelled')
                                    <span class="status-badge status-cancelled">ملغى</span>
                                @else
                                    <span class="status-badge">{{ $order->status }}</span>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="info-card">
                            <h5 class="section-title">معلومات العميل</h5>
                            <div class="info-label">اسم العميل</div>
                            <div class="info-value">{{ $order->customer->name ?? 'اسم العميل' }}</div>

                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value">{{ $order->customer->email ?? 'غير متوفر' }}</div>

                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value">{{ $order->customer->phone ?? 'غير متوفر' }}</div>

                            <div class="info-label">العنوان</div>
                            <div class="info-value">{{ $order->customer->address ?? 'غير متوفر' }}</div>
                        </div>
                    </td>
                </tr>
            </table>

            <!-- Items Table -->
            <div style="margin-top: 20px;">
                <h5 class="section-title">تفاصيل الطلب</h5>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($order->items as $item)
                            <tr>
                                <td><strong>{{ $item->product->code }}</strong></td>
                                <td>{{ $item->product->name }}</td>
                                <td>{{ number_format($item->quantity, 0) }}</td>
                                <td style="direction: ltr;">{{ number_format($item->unit_price, 2) }} د.ت</td>
                                <td style="direction: ltr;"><strong>{{ number_format($item->total_price, 2) }}
                                        د.ت</strong></td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Totals -->
            <div class="row mt-4">
                <div class="col-md-6"></div>
                <div class="col-md-6">
                    <div class="total-section">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span><strong>{{ number_format($order->items->sum('total_price'), 2) }}
                                    د.ت</strong></span>
                        </div>

                        @if ($order->tax_amount > 0)
                            <div class="total-row">
                                <span>الضريبة (19%):</span>
                                <span><strong>{{ number_format($order->tax_amount, 2) }} د.ت</strong></span>
                            </div>
                        @endif

                        @if ($order->discount_amount > 0)
                            <div class="total-row">
                                <span>الخصم:</span>
                                <span><strong>-{{ number_format($order->discount_amount, 2) }} د.ت</strong></span>
                            </div>
                        @endif

                        <div class="total-row final">
                            <span>المجموع الإجمالي:</span>
                            <span>{{ number_format($order->total_amount, 2) }} د.ت</span>
                        </div>
                    </div>
                </div>
                <!-- Totals -->
                @php
                    $subtotal = $order->total_amount;
                    $taxRate = 0.19; // Taxe de 19%
                    $taxAmount = $subtotal * $taxRate;
                    $grandTotal = $subtotal + $taxAmount;
                @endphp
                <table class="totals-table">
                    <tr>
                        <td>المجموع الفرعي:</td>
                        <td style="text-align: left; direction: ltr;">
                            <strong>{{ number_format($subtotal, 2) }} ريال</strong>
                        </td>
                    </tr>
                    <tr>
                        <td>الضريبة ({{ $taxRate * 100 }}%):</td>
                        <td style="text-align: left; direction: ltr;">
                            <strong>{{ number_format($taxAmount, 2) }} ريال</strong>
                        </td>
                    </tr>
                    <tr class="final-total">
                        <td>المجموع الإجمالي:</td>
                        <td style="text-align: left; direction: ltr;">{{ number_format($grandTotal, 2) }} ريال</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Footer -->
        <div class="invoice-footer">
            <div class="row">
                <div class="col-md-6">
                    <h6>شكراً لثقتكم بنا!</h6>
                    <p class="mb-0">نتطلع للتعامل معكم مرة أخرى</p>
                </div>
                <div class="col-md-6 text-start">
                    <p class="mb-1"><strong>طرق الدفع:</strong> نقداً | بنكياً | إلكترونياً</p>
                    <p class="mb-0"><strong>ملاحظة:</strong> يرجى الاحتفاظ بهذه الفاتورة للمراجعة</p>
                </div>
                <!-- Footer -->
                <div class="invoice-footer">
                    <p>شكراً لثقتكم بنا! نتطلع للتعامل معكم مرة أخرى.</p>
                    <p style="font-size: 8pt;">يرجى الاحتفاظ بهذه الفاتورة للمراجعة.</p>
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Font Awesome for icons -->
        <script src="https://kit.fontawesome.com/your-fontawesome-key.js" crossorigin="anonymous"></script>
</body>

</html>
