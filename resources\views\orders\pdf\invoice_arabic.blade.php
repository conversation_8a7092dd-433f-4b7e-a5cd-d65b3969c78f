<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ $order->order_number }}</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            background-color: #f8f9fa;
            font-family: 'aealarabiya', sans-serif;
            /* Utilisation de la police définie dans le contrôleur */
            background-color: #ffffff;
            direction: rtl;
            font-size: 10pt;
        }

        .invoice-container {
            max-width: 900px;
            margin: 2rem auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
            padding: 10px;
        }

        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-color: #4a5568;
            /* Une couleur unie est préférable pour TCPDF */
            color: white;
            padding: 2rem;
            padding: 20px;
            text-align: center;
        }

        .company-logo {
            max-width: 120px;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .invoice-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            font-size: 24pt;
            font-weight: bold;
            margin: 0;
        }

        .invoice-body {
            padding: 2rem;

            .company-details p {
                margin: 0;
                font-size: 9pt;
            }

            .info-table {
                width: 100%;
                margin-top: 20px;
                margin-bottom: 20px;
            }

            .info-table td {
                width: 50%;
                vertical-align: top;
                padding: 5px;
            }

            .info-card {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                background-color: #f8f9fa;
            }

            .section-title {
                color: #495057;
                font-weight: 600;
                font-weight: bold;
                border-bottom: 2px solid #dee2e6;
                padding-bottom: 0.5rem;
                margin-bottom: 1rem;
                padding-bottom: 5px;
                margin-bottom: 10px;
                font-size: 12pt;
            }

            .client-info-card,
            .order-info-card {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 1.5rem;
                border-right: 4px solid #667eea;
                margin-bottom: 2rem;
            }

            .items-table {
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                overflow: hidden;
                margin-bottom: 2rem;
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }

            .items-table thead {
                background: linear-gradient(45deg, #667eea, #764ba2);
                background-color: #343a40;
                color: white;
            }

            .items-table th {
                font-weight: 600;
                padding: 1rem;
                border: none;
                font-weight: bold;
                padding: 8px;
                border: 1px solid #dee2e6;
                text-align: right;
            }

            .items-table td {
                padding: 1rem;
                padding: 8px;
                vertical-align: middle;
                border-top: 1px solid #dee2e6;
                border: 1px solid #dee2e6;
            }

            .items-table tbody tr:hover {
                background-color: #f1f3f4;

                .items-table tbody tr:nth-child(even) {
                    background-color: #f8f9fa;
                }

                .total-section {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 1.5rem;
                    border: 2px solid #dee2e6;

                    .totals-table {
                        width: 50%;
                        float: left;
                        /* Pour aligner à gauche en RTL */
                        margin-top: 20px;
                    }

                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 0.5rem;

                        .totals-table td {
                            padding: 5px 8px;
                        }

                        .total-row.final {
                            .totals-table .final-total {
                                font-size: 1.2rem;
                                font-weight: 700;
                                color: #495057;
                                font-weight: bold;
                                border-top: 2px solid #495057;
                                padding-top: 0.5rem;
                                margin-top: 1rem;
                                padding-top: 8px;
                            }

                            .invoice-footer {
                                background: #495057;
                                color: white;
                                padding: 1.5rem;
                                padding: 15px;
                                text-align: center;
                                margin-top: 30px;
                            }

                            .status-badge {
                                padding: 0.5rem 1rem;
                                padding: 4px 8px;
                                border-radius: 20px;
                                font-weight: 600;
                                text-transform: uppercase;
                                font-size: 0.85rem;
                                font-weight: bold;
                                font-size: 0.8rem;
                                color: #fff;
                            }

                            .status-paid {
                                background-color: #d4edda;
                                color: #155724;

                                .status-completed {
                                    background-color: #28a745;
                                }

                                .status-processing {
                                    background-color: #ffc107;
                                    color: #000;
                                }

                                .status-pending {
                                    background-color: #fff3cd;
                                    color: #856404;
                                    background-color: #dc3545;
                                }

                                .status-cancelled {
                                    background-color: #f8d7da;
                                    color: #721c24;
                                    background-color: #6c757d;
                                }

                                @media print {
                                    body {
                                        background: white !important;
                                    }

                                    .invoice-container {
                                        box-shadow: none !important;
                                        margin: 0 !important;
                                        max-width: none !important;
                                    }
                                }

                                .info-label {
                                    font-weight: 600;
                                    font-weight: bold;
                                    color: #6c757d;
                                    margin-bottom: 0.25rem;
                                }

                                .info-value {
                                    font-weight: 500;
                                    color: #495057;
                                    margin-bottom: 1rem;
                                    margin-bottom: 8px;
                                }
    </style>
</head>

<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <table style="width: 100%;" class="invoice-header">
                        <tr>
                            <td style="width: 25%; text-align: right;">
                                @php
                                    $logoPath = public_path('assets/images/logo.png');
                                @endphp
                                @if (file_exists($logoPath))
                                    <img src="{{ $logoPath }}" alt="Logo" class="logo">
                                    <img src="{{ $logoPath }}" alt="Logo" class="company-logo">
                                @else
                                    <div style="font-size: 20px; font-weight: bold; color: #ccc;">LOGO</div>
                                @endif
                </div>
                <div class="col-md-6 text-center">
                    <h1 class="invoice-title mb-3">فـاتـورة</h1>
                    <h4 class="mb-0">الإدارة العامة للأشغال</h4>
                </div>
                <div class="col-md-3 text-center">
                    <div class="mb-2">
                        <small>رقم الفاتورة</small>
                        <h5 class="mb-0">#{{ $order->order_number }}</h5>
                        </td>
                        <td style="width: 50%; text-align: center;">
                            <h1 class="invoice-title">فـاتـورة</h1>
                            <h4>الإدارة العامة للأشغال</h4>
                        </td>
                        <td style="width: 25%; text-align: left; vertical-align: top;">
                            <div class="company-details">
                                <p>رقم الفاتورة: #{{ $order->order_number }}</p>
                                <p>القاعدة العسكرية بالعوينة</p>
                                <p>الهاتف: 28341663</p>
                                <p><EMAIL></p>
                            </div>
                    </div>
                </div>
                </td>
                </tr>
                </table>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>القاعدة العسكرية بالعوينة</div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="mb-1"><i class="fas fa-phone me-2"></i>28341663</div>
                    </div>
                    <div class="col-md-4 text-start">
                        <div class="mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Body -->
        <div class="invoice-body">
            <table class="info-table">
                <tr>
                    <td>
                        <div class="info-card">
                            <h5 class="section-title">معلومات الطلب</h5>
                            <div class="info-label">رقم الطلب</div>
                            <div class="info-value">#{{ $order->order_number }}</div>

                            <!-- Body -->
                            <div class="invoice-body">
                                <div class="row">
                                    <!-- Client Information -->
                                    <div class="col-md-6">
                                        <div class="client-info-card">
                                            <h5 class="section-title">معلومات العميل</h5>
                                            <div class="info-label">اسم العميل</div>
                                            <div class="info-value">{{ $customer->name ?? 'اسم العميل' }}</div>
                                            <div class="info-label">تاريخ الإنشاء</div>
                                            <div class="info-value">{{ $order->created_at->format('Y/m/d - H:i') }}
                                            </div>

                                            <div class="info-label">رقم الهاتف</div>
                                            <div class="info-value">{{ $customer->phone ?? '+************' }}</div>
                                            <div class="info-label">حالة الطلب</div>
                                            <div class="info-value">
                                                @if ($order->status === 'completed')
                                                    <span class="status-badge status-completed">مكتملة</span>
                                                @elseif($order->status === 'processing')
                                                    <span class="status-badge status-processing">قيد المعالجة</span>
                                                @elseif($order->status === 'pending')
                                                    <span class="status-badge status-pending">في الانتظار</span>
                                                @elseif($order->status === 'cancelled')
                                                    <span class="status-badge status-cancelled">ملغاة</span>
                                                @else
                                                    <span class="status-badge">{{ $order->status }}</span>
                                                @endif
                                            </div>
                                        </div>
                    </td>
                    <td>
                        <div class="info-card">
                            <h5 class="section-title">معلومات العميل</h5>
                            <div class="info-label">اسم العميل</div>
                            <div class="info-value">{{ $order->customer->name ?? 'اسم العميل' }}</div>

                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value">{{ $customer->email ?? '<EMAIL>' }}</div>
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value">{{ $order->customer->phone ?? 'غير متوفر' }}</div>

                            <div class="info-label">العنوان</div>
                            <div class="info-value">{{ $customer->address ?? 'عنوان العميل' }}</div>
                        </div>
        </div>
        <div class="info-label">البريد الإلكتروني</div>
        <div class="info-value">{{ $order->customer->email ?? 'غير متوفر' }}</div>

        <!-- Order Information -->
        <div class="col-md-6">
            <div class="order-info-card">
                <h5 class="section-title">معلومات الطلب</h5>
                <div class="info-label">رقم الطلب</div>
                <div class="info-value">#{{ $order->order_number }}</div>
                <div class="info-label">العنوان</div>
                <div class="info-value">{{ $order->customer->address ?? 'غير متوفر' }}</div>
            </div>
            </td>
            </tr>
            </table>

            <div class="info-label">تاريخ الإنشاء</div>
            <div class="info-value">{{ $order->created_at->format('Y/m/d - H:i') }}</div>

            <div class="info-label">حالة الطلب</div>
            <div class="info-value">
                @if ($order->status === 'completed')
                    <span class="status-badge status-completed">مكتمل</span>
                @elseif($order->status === 'processing')
                    <span class="status-badge status-processing">قيد المعالجة</span>
                @elseif($order->status === 'pending')
                    <span class="status-badge status-pending">في الانتظار</span>
                @elseif($order->status === 'cancelled')
                    <span class="status-badge status-cancelled">ملغى</span>
                @else
                    <span class="status-badge">{{ $order->status }}</span>
                @endif
            </div>
        </div>
    </div>
    </div>

    <!-- Items Table -->
    <div class="mt-4">
        <h5 class="section-title">تفاصيل الطلب</h5>
        <div class="table-responsive">
            <table class="table items-table mb-0">
                <!-- Items Table -->
                <div style="margin-top: 20px;">
                    <h5 class="section-title">تفاصيل الطلب</h5>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم المنتج</th>
                                <th class="text-center">الكمية</th>
                                <th class="text-center">سعر الوحدة</th>
                                <th class="text-center">المجموع</th>
                                <th style="text-align: center;">الكمية</th>
                                <th style="text-align: left;">سعر الوحدة</th>
                                <th style="text-align: left;">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($order->items as $item)
                                <tr>
                                    <td><strong>{{ $item->product->code }}</strong></td>
                                    <td>{{ $item->product->name }}</td>
                                    <td class="text-center">{{ number_format($item->quantity, 0) }}</td>
                                    <td class="text-center">{{ number_format($item->unit_price, 2) }} د.ت</td>
                                    <td class="text-center"><strong>{{ number_format($item->total_price, 2) }}
                                            د.ت</strong></td>
                                    <td style="text-align: center;">{{ number_format($item->quantity, 0) }}</td>
                                    <td style="text-align: left; direction: ltr;">
                                        {{ number_format($item->unit_price, 2) }}
                                        ريال</td>
                                    <td style="text-align: left; direction: ltr;">
                                        <strong>{{ number_format($item->total_price, 2) }} ريال</strong>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
        </div>

        <!-- Totals -->
        <div class="row mt-4">
            <div class="col-md-6"></div>
            <div class="col-md-6">
                <div class="total-section">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span><strong>{{ number_format($order->items->sum('total_price'), 2) }} د.ت</strong></span>
                    </div>

                    @if ($order->tax_amount > 0)
                        <div class="total-row">
                            <span>الضريبة (19%):</span>
                            <span><strong>{{ number_format($order->tax_amount, 2) }} د.ت</strong></span>
                        </div>
                    @endif

                    @if ($order->discount_amount > 0)
                        <div class="total-row">
                            <span>الخصم:</span>
                            <span><strong>-{{ number_format($order->discount_amount, 2) }} د.ت</strong></span>
                        </div>
                    @endif

                    <div class="total-row final">
                        <span>المجموع الإجمالي:</span>
                        <span>{{ number_format($order->total_amount, 2) }} د.ت</span>
                    </div>
                </div>
            </div>
            <!-- Totals -->
            @php
                $subtotal = $order->total_amount;
                $taxRate = 0.19; // Taxe de 19%
                $taxAmount = $subtotal * $taxRate;
                $grandTotal = $subtotal + $taxAmount;
            @endphp
            <table class="totals-table">
                <tr>
                    <td>المجموع الفرعي:</td>
                    <td style="text-align: left; direction: ltr;">
                        <strong>{{ number_format($subtotal, 2) }} ريال</strong>
                    </td>
                </tr>
                <tr>
                    <td>الضريبة ({{ $taxRate * 100 }}%):</td>
                    <td style="text-align: left; direction: ltr;">
                        <strong>{{ number_format($taxAmount, 2) }} ريال</strong>
                    </td>
                </tr>
                <tr class="final-total">
                    <td>المجموع الإجمالي:</td>
                    <td style="text-align: left; direction: ltr;">{{ number_format($grandTotal, 2) }} ريال</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Footer -->
    <div class="invoice-footer">
        <div class="row">
            <div class="col-md-6">
                <h6>شكراً لثقتكم بنا!</h6>
                <p class="mb-0">نتطلع للتعامل معكم مرة أخرى</p>
            </div>
            <div class="col-md-6 text-start">
                <p class="mb-1"><strong>طرق الدفع:</strong> نقداً | بنكياً | إلكترونياً</p>
                <p class="mb-0"><strong>ملاحظة:</strong> يرجى الاحتفاظ بهذه الفاتورة للمراجعة</p>
            </div>
            <!-- Footer -->
            <div class="invoice-footer">
                <p>شكراً لثقتكم بنا! نتطلع للتعامل معكم مرة أخرى.</p>
                <p style="font-size: 8pt;">يرجى الاحتفاظ بهذه الفاتورة للمراجعة.</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome for icons -->
    <script src="https://kit.fontawesome.com/your-fontawesome-key.js" crossorigin="anonymous"></script>
</body>

</html>
