<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ $order->order_number }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.8/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'aealarabiya', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 10px;
            /* Reduced padding */
            color: #212529;
            /* Bootstrap text color */
            font-size: 11px;
            /* Reduced font size */
        }

        .header-table {
            width: 100%;
            margin-bottom: 20px;
        }

        .logo {
            width: 150px;
        }

        .company-info {
            text-align: right;
        }

        .company-info h2 {
            margin: 0;
            font-size: 16px;
            color: #0d6efd;
            /* Bootstrap primary color */
        }

        .company-info p {
            margin: 2px 0;
            font-size: 11px;
            color: #6c757d;
            /* Bootstrap secondary text color */
        }

        .invoice-title {
            text-align: center;
            margin-bottom: 20px;
        }

        .invoice-title h1 {
            color: #0d6efd;
            font-size: 28px;
            margin: 0;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .invoice-title .subtitle {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 5px;
        }

        .info-table-container {
            width: 100%;
            margin-bottom: 20px;
        }

        .info-section {
            border: 1px solid #dee2e6;
            /* Bootstrap card border */
            border-radius: 4px;
            padding: 10px;
            height: 120px;
            /* Fixed height for alignment */
        }

        .info-section h3 {
            color: #0d6efd;
            font-size: 13px;
            margin-top: 0;
            margin-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .info-line {
            margin-bottom: 4px;
        }

        .info-line strong {
            color: #000;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 10px;
            color: #fff;
        }

        .status-completed {
            background-color: #198754;
        }

        /* Bootstrap success */
        .status-processing {
            background-color: #ffc107;
            color: #000;
        }

        /* Bootstrap warning */
        .status-pending {
            background-color: #dc3545;
        }

        /* Bootstrap danger */
        .status-cancelled {
            background-color: #6c757d;
        }

        /* Bootstrap secondary */

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .items-table th {
            background-color: #212529;
            /* Bootstrap dark */
            color: white;
            padding: 8px;
            text-align: right;
            font-weight: bold;
        }

        .items-table td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
            /* Bootstrap table-striped */
        }

        .items-table .text-left {
            text-align: left;
        }

        .items-table .text-center {
            text-align: center;
        }

        .total-section {
            margin-top: 15px;
            text-align: left;
            width: 40%;
            margin-right: 60%;
            /* Push to the left */
            border-collapse: collapse;
        }

        .total-section td {
            padding: 4px 8px;
            font-size: 12px;
        }

        .total-section .total-final td {
            font-size: 14px;
            font-weight: bold;
            color: #0d6efd;
            border-top: 2px solid #0d6efd;
            padding-top: 8px;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 9px;
            color: #7f8c8d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }

        .notes {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-right: 3px solid #0d6efd;
        }

        .notes h4 {
            margin-top: 0;
            font-size: 12px;
            color: #0d6efd;
        }
    </style>
</head>

<body>
    <!-- Header with Logo and Company Info -->
    <table class="header-table">
        <tr>
            <td style="width: 30%; text-align: left; vertical-align: top;">
                {{-- Pour que le logo s'affiche, placez votre fichier logo.png dans le dossier public/assets/images/ --}}
                @php
                    $logoPath = public_path('assets/images/logo.png');
                @endphp
                @if (file_exists($logoPath))
                    <img src="{{ $logoPath }}" alt="Logo" class="logo">
                @else
                    <div style="font-size: 20px; font-weight: bold; color: #ccc;">LOGO</div>
                @endif
            </td>
            <td style="width: 70%; text-align: right; vertical-align: top;">
                <div class="company-info">
                    <h2>الإدارة العامة للأشغال</h2>
                    <p>العنوان: القاعدة العسكرية بالعوينة</p>
                    <p>الهاتف: 28341663</p>
                    <p>البريد الإلكتروني: <EMAIL></p>
                </div>
            </td>
        </tr>
    </table>

    <!-- Invoice Title -->
    <div class="invoice-title">
        <h1>فاتورة البيع</h1>
        <div class="subtitle">
            رقم الطلب: {{ $order->order_number }} | تاريخ الطلب: {{ $order->created_at->format('Y/m/d') }}
        </div>
    </div>

    <!-- Customer and Order Information -->
    <table class="info-table-container">
        <tr>
            <td style="width: 50%; padding-left: 5px; vertical-align: top;">
                <div class="info-section">
                    <h3>تفاصيل الطلب</h3>
                    <div class="info-line"><strong>رقم الطلب:</strong> {{ $order->order_number }}</div>
                    <div class="info-line"><strong>التاريخ:</strong> {{ $order->created_at->format('Y/m/d H:i') }}</div>
                    <div class="info-line">
                        <strong>الحالة:</strong>
                        <span class="status-badge status-{{ $order->status }}">
                            @switch($order->status)
                                @case('completed')
                                    مكتملة
                                @break

                                @case('processing')
                                    قيد المعالجة
                                @break

                                @case('pending')
                                    في الانتظار
                                @break

                                @case('cancelled')
                                    ملغية
                                @break

                                @default
                                    {{ $order->status }}
                            @endswitch
                        </span>
                    </div>
                </div>
            </td>
            <td style="width: 50%; padding-right: 5px; vertical-align: top;">
                <div class="info-section">
                    <h3>معلومات العميل</h3>
                    <div class="info-line"><strong>الاسم:</strong> {{ $order->customer->name }}</div>
                    <div class="info-line"><strong>البريد الإلكتروني:</strong> {{ $order->customer->email }}</div>
                    @if ($order->customer->phone)
                        <div class="info-line"><strong>رقم الهاتف:</strong> {{ $order->customer->phone }}</div>
                    @endif
                    @if ($order->customer->address)
                        <div class="info-line"><strong>العنوان:</strong> {{ $order->customer->address }}</div>
                    @endif
                </div>
            </td>
        </tr>
    </table>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 15%;">كود المنتج</th>
                <th style="width: 35%;">اسم المنتج</th>
                <th style="width: 15%;" class="text-center">الكمية</th>
                <th style="width: 15%;" class="text-left">السعر الوحدة</th>
                <th style="width: 20%;" class="text-left">الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product->code }}</td>
                    <td>{{ $item->product->name }}</td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-left" style="direction: ltr;">{{ number_format($item->unit_price, 2, '.', '') }}
                        ريال</td>
                    <td class="text-left" style="direction: ltr;">{{ number_format($item->total_price, 2, '.', '') }}
                        ريال</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals and Notes -->
    <table style="width: 100%;">
        <tr>
            <td style="width: 50%; vertical-align: top;">
                @if ($order->notes)
                    <div class="notes">
                        <h4>ملاحظات:</h4>
                        <p>{{ $order->notes }}</p>
                    </div>
                @endif
            </td>
            <td style="width: 50%; vertical-align: top;">
                <table class="total-section">
                    <tr>
                        <td style="text-align: right;"><strong>المجموع الفرعي:</strong></td>
                        <td style="text-align: left; direction: ltr;">
                            {{ number_format($order->items->sum('total_price'), 2, '.', ',') }} ريال</td>
                    </tr>
                    <tr>
                        <td style="text-align: right;"><strong>الضريبة (15%):</strong></td>
                        <td style="text-align: left; direction: ltr;">
                            {{ number_format($order->total_amount * 0.15, 2, '.', ',') }} ريال</td>
                    </tr>
                    <tr class="total-final">
                        <td style="text-align: right;"><strong>المجموع الكلي:</strong></td>
                        <td style="text-align: left; direction: ltr;">
                            {{ number_format($order->total_amount * 1.15, 2, '.', ',') }} ريال</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذه الفاتورة تلقائياً من نظام الإدارة العربي | شكراً لكم على تعاملكم معنا</p>
        <p>تاريخ الطباعة: {{ date('Y/m/d H:i:s') }}</p>
    </div>
</body>

</html>
