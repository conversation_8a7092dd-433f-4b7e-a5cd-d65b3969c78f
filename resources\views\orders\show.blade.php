<!-- resources/views/orders/show.blade.php -->
@extends('layouts.app')

@section('title', 'Détails de la Commande')

@section('content')
    <div class="card">
        <div class="card-header">
            <h5>Commande: {{ $order->order_number }}</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>Informations client</h6>
                    <p><strong>Nom:</strong> {{ $order->customer->name }}</p>
                    <p><strong>Email:</strong> {{ $order->customer->email }}</p>
                    <p><strong>Téléphone:</strong> {{ $order->customer->phone }}</p>
                    <p><strong>Adresse:</strong> {{ $order->customer->address }}</p>
                </div>
                <div class="col-md-6">
                    <h6>Informations commande</h6>
                    <p><strong>Statut:</strong>
                        <span
                            class="badge bg-{{ $order->status == 'completed' ? 'success' : ($order->status == 'processing' ? 'warning' : 'secondary') }}">
                            {{ $order->status }}
                        </span>
                    </p>
                    <p><strong>Date:</strong> {{ $order->created_at->format('d/m/Y H:i') }}</p>
                    <p><strong>Total:</strong> {{ number_format($order->total_amount, 2) }} €</p>
                    @if ($order->notes)
                        <p><strong>Notes:</strong> {{ $order->notes }}</p>
                    @endif
                </div>

                <!-- Ajoutez cette section après les informations de la commande -->
                @if (!in_array($order->status, ['completed', 'cancelled']))
                    <div class="mt-3">
                        <h6>Changer le statut</h6>
                        <form action="{{ route('orders.update-status', $order->id) }}" method="POST" class="d-inline">
                            @csrf
                            <div class="input-group" style="max-width: 300px;">
                                <select name="status" class="form-select">
                                    <option value="processing" {{ $order->status == 'processing' ? 'selected' : '' }}>En
                                        traitement</option>
                                    <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>
                                        Complétée</option>
                                    <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>Annulée
                                    </option>
                                </select>
                                <button type="submit" class="btn btn-outline-primary">Changer</button>
                            </div>
                        </form>
                    </div>
                @endif
            </div>

            <h6>Articles de la commande</h6>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th>Prix unitaire</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($order->items as $item)
                            <tr>
                                <td>{{ $item->product->name }}</td>
                                <td>{{ number_format($item->unit_price, 2) }} €</td>
                                <td>{{ $item->quantity }}</td>
                                <td>{{ number_format($item->total_price, 2) }} €</td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Total:</strong></td>
                            <td><strong>{{ number_format($order->total_amount, 2) }} €</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="mt-3">
                <a href="{{ route('orders.index') }}" class="btn btn-secondary">Retour</a>
                <a href="{{ route('orders.edit', $order->id) }}" class="btn btn-warning">Modifier</a>
                <a href="{{ route('orders.export.pdf', $order) }}" class="btn btn-success">
                    <i class="bi bi-file-pdf"></i> تصدير فاتورة عربية
                </a>
            </div>
        </div>
    </div>
@endsection
