<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات المستخدم - {{ $user->name }}</title>
    <style>
        body {
            font-family: 'a<PERSON><PERSON><PERSON><PERSON>', <PERSON>l, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2980b9;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2980b9;
            font-size: 24px;
            margin: 0;
            font-weight: bold;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 10px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            background-color: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            font-weight: bold;
            margin: 20px auto;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            background-color: #ecf0f1;
            padding: 10px 15px;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 16px;
            border-right: 4px solid #3498db;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .info-table td {
            padding: 12px 15px;
            border: 1px solid #bdc3c7;
        }

        .info-table .label {
            background-color: #ecf0f1;
            font-weight: bold;
            width: 30%;
            text-align: center;
        }

        .info-table .value {
            background-color: #ffffff;
            text-align: right;
        }

        .status-verified {
            background-color: #d5e8d4;
            color: #27ae60;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .status-unverified {
            background-color: #f8d7da;
            color: #e74c3c;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .qr-section {
            text-align: center;
            margin: 30px 0;
        }

        .qr-placeholder {
            width: 100px;
            height: 100px;
            border: 2px solid #bdc3c7;
            background-color: #ecf0f1;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 12px;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 15px;
        }

        .no-data {
            color: #7f8c8d;
            font-style: italic;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <h1>بيانات المستخدم</h1>
        <div class="subtitle">
            رقم المستخدم: #{{ $user->id }} | تاريخ الطباعة: {{ date('Y-m-d H:i:s') }}
        </div>
    </div>

    <!-- User Avatar -->
    <div class="avatar">
        {{ strtoupper(substr($user->name, 0, 1)) }}
    </div>

    <!-- Personal Information Section -->
    <div class="section">
        <div class="section-title">المعلومات الشخصية</div>

        <table class="info-table">
            <tr>
                <td class="label">الاسم الكامل</td>
                <td class="value">{{ $user->name }}</td>
            </tr>
            <tr>
                <td class="label">البريد الإلكتروني</td>
                <td class="value">{{ $user->email }}</td>
            </tr>
            <tr>
                <td class="label">رقم الهاتف</td>
                <td class="value">
                    @if ($user->phone)
                        {{ $user->phone }}
                    @else
                        <span class="no-data">غير محدد</span>
                    @endif
                </td>
            </tr>
            @if ($user->address)
                <tr>
                    <td class="label">العنوان</td>
                    <td class="value">{{ $user->address }}</td>
                </tr>
            @endif
        </table>
    </div>

    <!-- Account Information Section -->
    <div class="section">
        <div class="section-title">معلومات الحساب</div>

        <table class="info-table">
            <tr>
                <td class="label">رقم المستخدم</td>
                <td class="value">#{{ $user->id }}</td>
            </tr>
            <tr>
                <td class="label">حالة التحقق</td>
                <td class="value">
                    @if ($user->email_verified_at)
                        <span class="status-verified">تم التحقق في
                            {{ $user->email_verified_at->format('Y-m-d') }}</span>
                    @else
                        <span class="status-unverified">لم يتم التحقق بعد</span>
                    @endif
                </td>
            </tr>
            <tr>
                <td class="label">تاريخ الانضمام</td>
                <td class="value">{{ $user->created_at->format('Y-m-d H:i') }}</td>
            </tr>
            <tr>
                <td class="label">آخر تحديث</td>
                <td class="value">{{ $user->updated_at->format('Y-m-d H:i') }}</td>
            </tr>
        </table>
    </div>

    <!-- QR Code Section -->
    <div class="qr-section">
        <div class="section-title" style="text-align: center; border: none; background: none; font-size: 14px;">
            رمز الاستجابة السريعة للملف الشخصي
        </div>
        <div class="qr-placeholder">
            QR Code
        </div>
        <div style="font-size: 10px; color: #7f8c8d;">
            رمز QR للوصول السريع لملف المستخدم
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير تلقائياً من نظام إدارة المستخدمين</p>
        <p>تاريخ الإنشاء: {{ date('Y-m-d H:i:s') }}</p>
    </div>
</body>

</html>
