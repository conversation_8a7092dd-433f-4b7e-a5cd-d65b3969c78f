<!-- resources/views/orders/edit.blade.php -->
@extends('layouts.app')

@section('title', 'Modifier la Commande')

@section('content')
    <div class="card">
        <div class="card-header">
            <h5>Modifier la Commande: {{ $order->order_number }}</h5>
        </div>
        <div class="card-body">
            <form id="orderForm" action="{{ route('orders.update', $order->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Client *</label>
                        <select name="customer_id" class="form-select" required>
                            <option value="">Sélectionner un client</option>
                            @foreach ($customers as $customer)
                                <option value="{{ $customer->id }}"
                                    {{ $order->customer_id == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->name }} - {{ $customer->email }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Statut *</label>
                        <select name="status" class="form-select" required>
                            <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>En attente</option>
                            <option value="processing" {{ $order->status == 'processing' ? 'selected' : '' }}>En traitement
                            </option>
                            <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>Complétée
                            </option>
                            <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>Annulée</option>
                        </select>
                    </div>
                </div>

                <div class="mb-4">
                    <h6>Articles de la commande</h6>
                    <div id="orderItems">
                        @foreach ($order->items as $index => $item)
                            <div class="item-row mb-3">
                                <div class="row">
                                    <div class="col-md-5">
                                        <select name="items[{{ $index }}][product_id]"
                                            class="form-select product-select" required>
                                            <option value="">Sélectionner un produit</option>
                                            @foreach ($products as $product)
                                                <option value="{{ $product->id }}" data-price="{{ $product->price }}"
                                                    {{ $item->product_id == $product->id ? 'selected' : '' }}>
                                                    {{ $product->name }} - {{ $product->price }}€
                                                    (Stock: {{ $product->quantity }})
                                                </option>
                                            @endforeach
                                        </select>
                                        <input type="hidden" name="items[{{ $index }}][id]"
                                            value="{{ $item->id }}">
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" name="items[{{ $index }}][quantity]"
                                            class="form-control quantity" min="1" value="{{ $item->quantity }}"
                                            required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control total" readonly
                                            value="{{ number_format($item->total_price, 2) }} €">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger remove-item">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <button type="button" id="addItem" class="btn btn-success mt-2">
                        <i class="fas fa-plus"></i> Ajouter un article
                    </button>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="3">{{ $order->notes }}</textarea>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>Total de la commande: <span
                                        id="grandTotal">{{ number_format($order->total_amount, 2) }}</span> €</h6>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Mettre à jour</button>
                <a href="{{ route('orders.show', $order->id) }}" class="btn btn-secondary">Annuler</a>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            let itemCount = {{ count($order->items) }};

            // Ajouter un nouvel article
            $('#addItem').click(function() {
                const newItem = `
            <div class="item-row mb-3">
                <div class="row">
                    <div class="col-md-5">
                        <select name="items[${itemCount}][product_id]" class="form-select product-select" required>
                            <option value="">Sélectionner un produit</option>
                            @foreach ($products as $product)
                                <option value="{{ $product->id }}" data-price="{{ $product->price }}">
                                    {{ $product->name }} - {{ $product->price }}€ (Stock: {{ $product->quantity }})
                                </option>
                            @endforeach
                        </select>
                        <input type="hidden" name="items[${itemCount}][id]" value="">
                    </div>
                    <div class="col-md-2">
                        <input type="number" name="items[${itemCount}][quantity]" class="form-control quantity" min="1" value="1" required>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control total" readonly value="0.00 €">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger remove-item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
                $('#orderItems').append(newItem);
                itemCount++;
            });

            // Supprimer un article
            $(document).on('click', '.remove-item', function() {
                if ($('.item-row').length > 1) {
                    $(this).closest('.item-row').remove();
                    calculateTotal();
                }
            });

            // Calculer le total lorsqu'un produit ou la quantité change
            $(document).on('change', '.product-select, .quantity', function() {
                const row = $(this).closest('.item-row');
                const productSelect = row.find('.product-select');
                const quantityInput = row.find('.quantity');
                const totalInput = row.find('.total');

                if (productSelect.val() && quantityInput.val()) {
                    const price = parseFloat(productSelect.find('option:selected').data('price'));
                    const quantity = parseInt(quantityInput.val());
                    const total = price * quantity;

                    totalInput.val(total.toFixed(2) + ' €');
                } else {
                    totalInput.val('0.00 €');
                }

                calculateTotal();
            });

            // Calculer le total général
            function calculateTotal() {
                let grandTotal = 0;

                $('.item-row').each(function() {
                    const totalText = $(this).find('.total').val();
                    if (totalText) {
                        const total = parseFloat(totalText.replace(' €', ''));
                        if (!isNaN(total)) {
                            grandTotal += total;
                        }
                    }
                });

                $('#grandTotal').text(grandTotal.toFixed(2));
            }

            // Validation du formulaire
            $('#orderForm').on('submit', function(e) {
                let isValid = true;

                $('.product-select').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Veuillez sélectionner un produit pour tous les articles.');
                }
            });

            // Initialiser le calcul du total au chargement
            calculateTotal();
        });
    </script>
@endpush
