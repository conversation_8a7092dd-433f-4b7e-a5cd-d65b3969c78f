<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ArabicUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $arabicUsers = [
            [
                'name' => 'أحمد محمد علي',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'address' => 'الرياض، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'فاطمة عبدالله',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'address' => 'جدة، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'محمد صالح',
                'email' => '<EMAIL>',
                'phone' => '+966503456789',
                'address' => 'الدمام، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'عائشة حسن',
                'email' => '<EMAIL>',
                'phone' => '+966504567890',
                'address' => 'مكة المكرمة، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'خالد عبدالرحمن',
                'email' => '<EMAIL>',
                'phone' => '+966505678901',
                'address' => 'المدينة المنورة، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'زينب أحمد',
                'email' => '<EMAIL>',
                'phone' => '+966506789012',
                'address' => 'الطائف، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'يوسف محمود',
                'email' => '<EMAIL>',
                'phone' => '+966507890123',
                'address' => 'أبها، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'مريم عبدالعزيز',
                'email' => '<EMAIL>',
                'phone' => '+966508901234',
                'address' => 'تبوك، المملكة العربية السعودية',
                'password' => Hash::make('password'),
            ],
        ];

        foreach ($arabicUsers as $user) {
            User::create($user);
        }
    }
}
