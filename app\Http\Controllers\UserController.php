<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use PDF;
use Exception;

class UserController extends Controller
{
    public function index()
    {
        $users = User::paginate(10);
        return view('users.index', compact('users'));
    }

    public function show(User $user)
    {
        return view('users.show', compact('user'));
    }

    public function exportUserPdf(User $user)
    {
        // Create new TCPDF instance directly
        $pdf = new \TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('Laravel Arabic App');
        $pdf->SetAuthor('Arabic System');
        $pdf->SetTitle('بيانات المستخدم - ' . $user->name);
        $pdf->SetSubject('تفاصيل المستخدم');

        // Set margins
        $pdf->SetMargins(15, 20, 15);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Add RTL support for Arabic
        $pdf->setRTL(true);

        // Set font for Arabic - using fallback approach
        $fontSet = false;
        $fonts = ['aealarabiya', 'dejavusans', 'freesans', 'helvetica'];

        foreach ($fonts as $font) {
            try {
                $pdf->SetFont($font, '', 12);
                $fontSet = true;
                break;
            } catch (Exception $e) {
                continue;
            }
        }

        if (!$fontSet) {
            // Fallback to default font
            $pdf->SetFont('helvetica', '', 12);
        }

        // Add a page
        $pdf->AddPage();

        // Generate HTML content from Blade template
        $html = view('users.pdf.user_details', compact('user'))->render();

        // Write HTML content to PDF
        $pdf->writeHTML($html, true, false, true, false, '');

        // Generate filename with user name
        $filename = 'user_details_' . $user->id . '_' . str_replace(' ', '_', $user->name) . '.pdf';

        return $pdf->Output($filename, 'D');
    }

    public function exportPdf()
    {
        $users = User::all();

        // Create new TCPDF instance directly
        $pdf = new \TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('Laravel Arabic App');
        $pdf->SetAuthor('Arabic System');
        $pdf->SetTitle('قائمة المستخدمين');
        $pdf->SetSubject('تقرير المستخدمين');

        // Set margins
        $pdf->SetMargins(15, 20, 15);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Add RTL support for Arabic
        $pdf->setRTL(true);

        // Set font for Arabic - using fallback approach
        $fontSet = false;
        $fonts = ['aealarabiya', 'dejavusans', 'freesans', 'helvetica'];

        foreach ($fonts as $font) {
            try {
                $pdf->SetFont($font, '', 12);
                $fontSet = true;
                break;
            } catch (Exception $e) {
                continue;
            }
        }

        if (!$fontSet) {
            // Fallback to default font
            $pdf->SetFont('helvetica', '', 12);
        }

        // Add a page
        $pdf->AddPage();

        // Title
        $pdf->SetFont($fontSet ? $fonts[0] : 'helvetica', 'B', 20);
        $pdf->Cell(0, 15, 'قائمة المستخدمين', 0, 1, 'C');
        $pdf->Ln(10);

        // Table header
        $pdf->SetFont($fontSet ? $fonts[0] : 'helvetica', 'B', 14);
        $pdf->SetFillColor(200, 220, 255);
        $pdf->Cell(20, 10, 'الرقم', 1, 0, 'C', 1);
        $pdf->Cell(50, 10, 'الاسم', 1, 0, 'C', 1);
        $pdf->Cell(70, 10, 'البريد الإلكتروني', 1, 0, 'C', 1);
        $pdf->Cell(40, 10, 'الهاتف', 1, 1, 'C', 1);

        // Table content
        $pdf->SetFont($fontSet ? $fonts[0] : 'helvetica', '', 12);

        foreach ($users as $user) {
            $pdf->Cell(20, 10, $user->id, 1, 0, 'C');
            $pdf->Cell(50, 10, $user->name, 1, 0, 'C');
            $pdf->Cell(70, 10, $user->email, 1, 0, 'C');
            $pdf->Cell(40, 10, $user->phone ?? 'غير محدد', 1, 1, 'C');
        }

        // Add footer
        $pdf->Ln(20);
        $pdf->SetFont($fontSet ? $fonts[0] : 'helvetica', 'I', 10);
        $pdf->Cell(0, 10, 'تم إنشاء هذا التقرير في: ' . date('Y-m-d H:i:s'), 0, 1, 'C');

        return $pdf->Output('users_list_arabic.pdf', 'D');
    }
}
