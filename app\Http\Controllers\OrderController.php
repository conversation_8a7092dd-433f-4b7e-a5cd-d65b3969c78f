<?php

// app/Http/Controllers/OrderController.php
namespace App\Http\Controllers;


use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderController extends Controller
{
    public function index()
    {
        $orders = Order::with(['customer', 'items.product'])->latest()->get();
        return view('orders.index', compact('orders'));
    }

    public function create()
    {
        $customers = User::get();
        $products = Product::with('category')->get();
        return view('orders.create', compact('customers', 'products'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:users,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string'
        ]);

        $order = DB::transaction(function () use ($request) {
            $order = Order::create([
                'customer_id' => $request->customer_id,
                'total_amount' => 0,
                'notes' => $request->notes
            ]);

            $totalAmount = 0;

            foreach ($request->items as $item) {
                $product = Product::findOrFail($item['product_id']);

                $orderItem = $order->items()->create([
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $product->price,
                    'total_price' => $product->price * $item['quantity']
                ]);

                $totalAmount += $orderItem->total_price;

                // Mettre à jour le stock
                $product->decrement('quantity', $item['quantity']);
            }

            $order->update(['total_amount' => $totalAmount]);

            return $order;
        });

        return redirect()->route('orders.show', $order->id)
            ->with('success', 'Commande créée avec succès.');
    }

    public function show(Order $order)
    {
        $order->load(['customer', 'items.product']);
        return view('orders.show', compact('order'));
    }

    public function edit(Order $order)
    {
        // Empêcher la modification des commandes complétées ou annulées
        if (in_array($order->status, ['completed', 'cancelled'])) {
            return redirect()->route('orders.show', $order->id)
                ->with('error', 'Impossible de modifier une commande ' . $order->status . '.');
        }

        $customers = User::get();
        $products = Product::with('category')->get();
        $order->load(['items.product', 'customer']);

        return view('orders.edit', compact('order', 'customers', 'products'));
    }


    public function update(Request $request, Order $order)
    {
        // Empêcher la modification des commandes complétées ou annulées
        if (in_array($order->status, ['completed', 'cancelled'])) {
            return redirect()->route('orders.show', $order->id)
                ->with('error', 'Impossible de modifier une commande ' . $order->status . '.');
        }

        $request->validate([
            'customer_id' => 'required|exists:users,id',
            'status' => 'required|in:pending,processing,completed,cancelled',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.id' => 'nullable|exists:order_items,id',
            'notes' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            // Mettre à jour les informations de base de la commande
            $order->update([
                'customer_id' => $request->customer_id,
                'status' => $request->status,
                'notes' => $request->notes
            ]);

            $totalAmount = 0;
            $existingItemIds = [];
            $productStockUpdates = [];

            // Traiter chaque article de la commande
            foreach ($request->items as $itemData) {
                $product = Product::findOrFail($itemData['product_id']);
                $quantity = $itemData['quantity'];

                if (isset($itemData['id'])) {
                    // Mise à jour d'un article existant
                    $orderItem = OrderItem::find($itemData['id']);

                    if ($orderItem && $orderItem->order_id === $order->id) {
                        // Calculer la différence de quantité pour ajuster le stock
                        $quantityDifference = $quantity - $orderItem->quantity;

                        if ($quantityDifference != 0) {
                            $productStockUpdates[] = [
                                'product' => $product,
                                'quantity_change' => -$quantityDifference
                            ];
                        }

                        $orderItem->update([
                            'product_id' => $product->id,
                            'quantity' => $quantity,
                            'unit_price' => $product->price,
                            'total_price' => $product->price * $quantity
                        ]);

                        $existingItemIds[] = $orderItem->id;
                    }
                } else {
                    // Nouvel article
                    $orderItem = $order->items()->create([
                        'product_id' => $product->id,
                        'quantity' => $quantity,
                        'unit_price' => $product->price,
                        'total_price' => $product->price * $quantity
                    ]);

                    $productStockUpdates[] = [
                        'product' => $product,
                        'quantity_change' => -$quantity
                    ];

                    $existingItemIds[] = $orderItem->id;
                }

                $totalAmount += $orderItem->total_price;
            }

            // Supprimer les articles qui ne sont plus dans la commande
            $itemsToDelete = $order->items()->whereNotIn('id', $existingItemIds)->get();

            foreach ($itemsToDelete as $itemToDelete) {
                // Restaurer le stock pour les articles supprimés
                $productStockUpdates[] = [
                    'product' => $itemToDelete->product,
                    'quantity_change' => $itemToDelete->quantity
                ];
                $itemToDelete->delete();
            }

            // Mettre à jour les stocks des produits
            foreach ($productStockUpdates as $stockUpdate) {
                $stockUpdate['product']->increment('quantity', $stockUpdate['quantity_change']);
            }

            // Mettre à jour le montant total de la commande
            $order->update(['total_amount' => $totalAmount]);

            DB::commit();

            return redirect()->route('orders.show', $order->id)
                ->with('success', 'Commande mise à jour avec succès.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Erreur lors de la mise à jour: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function destroy(Order $order)
    {
        try {
            DB::beginTransaction();

            // Restaurer le stock pour tous les articles
            foreach ($order->items as $item) {
                $item->product->increment('quantity', $item->quantity);
            }

            // Supprimer la commande et ses articles
            $order->delete();

            DB::commit();

            return redirect()->route('orders.index')
                ->with('success', 'Commande supprimée avec succès.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('orders.index')
                ->with('error', 'Erreur lors de la suppression: ' . $e->getMessage());
        }
    }

    // Méthode supplémentaire pour changer le statut
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,completed,cancelled'
        ]);

        $oldStatus = $order->status;
        $order->update(['status' => $request->status]);

        // Si la commande est annulée, restaurer le stock
        if ($request->status === 'cancelled' && $oldStatus !== 'cancelled') {
            foreach ($order->items as $item) {
                $item->product->increment('quantity', $item->quantity);
            }
        }

        return redirect()->back()
            ->with('success', 'Statut de la commande mis à jour.');
    }

    public function exportOrderPdf(Order $order)
    {
        // Create new TCPDF instance directly
        $pdf = new \TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('Arabic System');
        $pdf->SetAuthor('Arabic System');
        $pdf->SetTitle('فاتورة الطلب - ' . $order->order_number);
        $pdf->SetSubject('فاتورة الطلب');

        // Set margins
        $pdf->SetMargins(15, 20, 15);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Add RTL support for Arabic
        $pdf->setRTL(true);

        // Set font for Arabic - using fallback approach
        $fontSet = false;
        $fonts = ['aealarabiya', 'dejavusans', 'freesans', 'helvetica'];

        foreach ($fonts as $font) {
            try {
                $pdf->SetFont($font, '', 12);
                $fontSet = true;
                break;
            } catch (Exception $e) {
                continue;
            }
        }

        if (!$fontSet) {
            // Fallback to default font
            $pdf->SetFont('helvetica', '', 12);
        }

        // Add a page
        $pdf->AddPage();

        // Generate HTML content from Blade template
        $html = view('orders.pdf.invoice_arabic', compact('order'))->render();

        // Write HTML content to PDF
        $pdf->writeHTML($html, true, false, true, false, '');

        // Generate filename
        $filename = 'فاتورة_' . $order->order_number . '_' . date('Y-m-d') . '.pdf';

        return $pdf->Output($filename, 'I');
    }
}
