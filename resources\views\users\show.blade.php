@extends('layouts.app')
@section('title', 'User Details')
@section('content')
    <div class="container">
        <!-- Header with Back Button -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary me-3">
                            <i class="bi bi-arrow-left"></i> Back to Users
                        </a>
                        <h1 class="h2 mb-0">User Details</h1>
                    </div>

                    <!-- Action Buttons -->
                    <div class="btn-group" role="group">
                        <a href="{{ route('users.export.user.pdf', $user) }}" class="btn btn-success">
                            <i class="bi bi-file-pdf"></i> تصدير PDF
                        </a>
                        <a href="{{ route('users.edit', $user) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <form action="{{ route('users.destroy', $user) }}" method="POST" class="d-inline"
                            onsubmit="return confirm('Are you sure you want to delete this user?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information Cards -->
        <div class="row">
            <!-- Profile Card -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <!-- Avatar -->
                        <div class="avatar mb-3">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                                style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                {{ strtoupper(substr($user->name, 0, 1)) }}
                            </div>
                        </div>

                        <h4 class="card-title">{{ $user->name }}</h4>
                        <p class="card-text text-muted">{{ $user->email }}</p>

                        <!-- Status Badge -->
                        @if ($user->email_verified_at)
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Verified
                            </span>
                        @else
                            <span class="badge bg-warning">
                                <i class="bi bi-exclamation-circle"></i> Not Verified
                            </span>
                        @endif

                        <hr>

                        <!-- Contact Information -->
                        <div class="text-start">
                            <p class="mb-2">
                                <i class="bi bi-envelope text-primary me-2"></i>
                                <a href="mailto:{{ $user->email }}" class="text-decoration-none">
                                    {{ $user->email }}
                                </a>
                            </p>

                            @if ($user->phone)
                                <p class="mb-2">
                                    <i class="bi bi-telephone text-success me-2"></i>
                                    <a href="tel:{{ $user->phone }}" class="text-decoration-none">
                                        {{ $user->phone }}
                                    </a>
                                </p>
                            @endif

                            @if ($user->address)
                                <p class="mb-0">
                                    <i class="bi bi-geo-alt text-info me-2"></i>
                                    {{ $user->address }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Details Card -->
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-lines-fill text-primary me-2"></i>
                            Personal Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">User ID</label>
                                    <p class="form-control-plaintext">#{{ $user->id }}</p>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Full Name</label>
                                    <p class="form-control-plaintext">{{ $user->name }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email Address</label>
                                    <p class="form-control-plaintext">
                                        <a href="mailto:{{ $user->email }}" class="text-decoration-none">
                                            {{ $user->email }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Phone Number</label>
                                    <p class="form-control-plaintext">
                                        @if ($user->phone)
                                            <a href="tel:{{ $user->phone }}" class="text-decoration-none">
                                                {{ $user->phone }}
                                            </a>
                                        @else
                                            <span class="text-muted fst-italic">Not provided</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>

                        @if ($user->address)
                            <div class="mb-3">
                                <label class="form-label text-muted">Address</label>
                                <p class="form-control-plaintext">{{ $user->address }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Account Information Card -->
                <div class="card shadow-sm mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-shield-check text-success me-2"></i>
                            Account Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email Verification</label>
                                    <p class="form-control-plaintext">
                                        @if ($user->email_verified_at)
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i>
                                                Verified on {{ $user->email_verified_at->format('M d, Y') }}
                                            </span>
                                        @else
                                            <span class="badge bg-warning">
                                                <i class="bi bi-exclamation-circle"></i>
                                                Not Verified
                                            </span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Member Since</label>
                                    <p class="form-control-plaintext">
                                        {{ $user->created_at->format('F d, Y \\a\\t g:i A') }}
                                        <small class="text-muted d-block">
                                            ({{ $user->created_at->diffForHumans() }})
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <p class="form-control-plaintext">
                                {{ $user->updated_at->format('F d, Y \\a\\t g:i A') }}
                                <small class="text-muted">
                                    ({{ $user->updated_at->diffForHumans() }})
                                </small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Timeline (Future Feature) -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history text-info me-2"></i>
                            Recent Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-inbox display-4"></i>
                            <p class="mt-2">No recent activity to display</p>
                            <small>User activity will appear here when available</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
