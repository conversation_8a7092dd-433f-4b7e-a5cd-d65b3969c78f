@extends('layouts.app')
@section('title', 'Categories List')
@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>Categories List</h2>
                    <div>
                        <a href="{{-- route('categories.create') --}}" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle"></i> Add Category
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if (isset($categories) && $categories->count() > 0)
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('categories.index') }}" class="d-flex">
                                <input type="text" name="search" class="form-control me-2"
                                    placeholder="Search categories..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-outline-secondary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <span class="badge bg-info fs-6">Total: {{ $categories->total() }} categories</span>
                            </div>
                        </div>
                    </div>

                    <!-- Categories Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Code</th>
                                    <th scope="col">Name</th>
                                    <th scope="col">Products Count</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($categories as $category)
                                    <tr>
                                        <th scope="row">{{ $category->id }}</th>
                                        <td>
                                            <code class="bg-light px-2 py-1 rounded">{{ $category->code }}</code>
                                        </td>
                                        <td class="fw-bold">{{ $category->name }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ $category->products_count }}</span>
                                        </td>

                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{-- route('categories.show', $category) --}}" class="btn btn-primary btn-sm"
                                                    title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{-- route('categories.edit', $category) --}}" class="btn btn-warning btn-sm"
                                                    title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{-- route('categories.destroy', $category) --}}" method="POST" style="display:inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete"
                                                        onclick="return confirm('Are you sure you want to delete this category?')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $categories->links('pagination::bootstrap-5') }}
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="bi bi-tags" style="font-size: 4rem; color: #6c757d;"></i>
                        <h4 class="mt-3 text-muted">No Categories Found</h4>
                        <p class="text-muted">Get started by adding your first category.</p>
                        <a href="{{-- route('categories.create') --}}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add First Category
                        </a>
                    </div>
                @endif
            </div>
        </div>
    @endsection
